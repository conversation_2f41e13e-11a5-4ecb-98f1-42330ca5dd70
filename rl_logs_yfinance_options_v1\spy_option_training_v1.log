2025-09-04 22:37:51,450 - INFO - SPY.py:2359 - Logging configured for Option Trader v1.
2025-09-04 22:37:51,451 - INFO - SPY.py:2360 - Log file: C:\Users\<USER>\Desktop\Spy option\rl_logs_yfinance_options_v1\spy_option_training_v1.log
2025-09-04 22:37:51,451 - INFO - SPY.py:2361 - Tickers to fetch: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-04 22:37:51,451 - INFO - SPY.py:2369 - YFinance session management disabled - letting yfinance handle its own sessions with curl_cffi.
2025-09-04 22:37:51,452 - INFO - test_dual_vix_integration.py:31 - === Testing Dual VIX Integration (VIX + VIX3M) ===
2025-09-04 22:37:51,452 - INFO - test_dual_vix_integration.py:39 - Testing with date range: 2025-06-06 to 2025-09-04
2025-09-04 22:37:51,452 - INFO - test_dual_vix_integration.py:42 - Step 1: Fetching data for all tickers...
2025-09-04 22:37:51,452 - INFO - test_dual_vix_integration.py:44 - All tickers: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-04 22:37:51,452 - INFO - test_dual_vix_integration.py:56 - Fetching data for SPY...
2025-09-04 22:37:53,241 - INFO - SPY.py:2508 - Using min_periods_override=30 for SPY
2025-09-04 22:37:53,245 - INFO - SPY.py:2651 - SPY expanded feature set: Keeping OHLC columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-04 22:37:53,245 - INFO - SPY.py:2657 - Using expanded OHLC feature set for SPY
2025-09-04 22:37:53,245 - INFO - SPY.py:2660 - SPY: Keeping OHLC columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-04 22:37:53,247 - INFO - SPY.py:2688 - Processed SPY data shape: (61, 4). Columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-04 22:37:53,247 - INFO - SPY.py:2689 - Data quality check: 0/244 (0.0%) zero values
2025-09-04 22:37:53,248 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_SPY_1756996671 SUCCESS in 1.80s [Ticker: SPY] [Operation: refactored_fetch]
2025-09-04 22:37:53,248 - INFO - test_dual_vix_integration.py:69 - Successfully fetched 61 rows for SPY
2025-09-04 22:37:53,248 - INFO - test_dual_vix_integration.py:56 - Fetching data for ^VIX...
2025-09-04 22:37:54,050 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-04 22:37:54,708 - INFO - SPY.py:2508 - Using min_periods_override=30 for ^VIX
2025-09-04 22:37:54,709 - INFO - SPY.py:2532 - Processing ^VIX data with shape (61, 7)
2025-09-04 22:37:54,709 - INFO - SPY.py:2533 - Raw ^VIX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-04 22:37:54,709 - INFO - SPY.py:2535 - Raw ^VIX Close values - first: 16.770000457763672, last: 16.350000381469727
2025-09-04 22:37:54,711 - INFO - SPY.py:2455 - Applying VIX validation for VIX
2025-09-04 22:37:54,711 - INFO - SPY.py:2614 - Processed VIX close values - first: 16.770000457763672, last: 16.350000381469727
2025-09-04 22:37:54,712 - INFO - SPY.py:2647 - Market index ^VIX: Keeping only ['close_VIX']
2025-09-04 22:37:54,713 - INFO - SPY.py:2688 - Processed ^VIX data shape: (61, 1). Columns: ['close_VIX']
2025-09-04 22:37:54,713 - INFO - SPY.py:2689 - Data quality check: 0/61 (0.0%) zero values
2025-09-04 22:37:54,713 - INFO - SPY.py:1161 - VALIDATION: ^VIX last value: 16.350000381469727
2025-09-04 22:37:54,713 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX_1756996673 SUCCESS in 1.46s [Ticker: ^VIX] [Operation: refactored_fetch]
2025-09-04 22:37:54,714 - INFO - test_dual_vix_integration.py:69 - Successfully fetched 61 rows for ^VIX
2025-09-04 22:37:54,714 - INFO - test_dual_vix_integration.py:56 - Fetching data for ^VIX3M...
2025-09-04 22:37:55,516 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 25s
2025-09-04 22:37:56,110 - INFO - SPY.py:2508 - Using min_periods_override=30 for ^VIX3M
2025-09-04 22:37:56,113 - INFO - SPY.py:2647 - Market index ^VIX3M: Keeping only ['close_VIX3M']
2025-09-04 22:37:56,114 - INFO - SPY.py:2688 - Processed ^VIX3M data shape: (61, 1). Columns: ['close_VIX3M']
2025-09-04 22:37:56,114 - INFO - SPY.py:2689 - Data quality check: 0/61 (0.0%) zero values
2025-09-04 22:37:56,114 - INFO - SPY.py:1161 - VALIDATION: ^VIX3M last value: 18.93000030517578
2025-09-04 22:37:56,114 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX3M_1756996674 SUCCESS in 1.40s [Ticker: ^VIX3M] [Operation: refactored_fetch]
2025-09-04 22:37:56,114 - INFO - test_dual_vix_integration.py:69 - Successfully fetched 61 rows for ^VIX3M
2025-09-04 22:37:56,115 - INFO - test_dual_vix_integration.py:56 - Fetching data for ^IRX...
2025-09-04 22:37:56,916 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-04 22:37:57,274 - INFO - SPY.py:2508 - Using min_periods_override=30 for ^IRX
2025-09-04 22:37:57,275 - INFO - SPY.py:2532 - Processing ^IRX data with shape (61, 7)
2025-09-04 22:37:57,275 - INFO - SPY.py:2533 - Raw ^IRX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-04 22:37:57,275 - INFO - SPY.py:2535 - Raw ^IRX Close values - first: 4.23199987411499, last: 4.01800012588501
2025-09-04 22:37:57,277 - INFO - SPY.py:2472 - Applying standardized Treasury rate validation for IRX (3-month Treasury yield)
2025-09-04 22:37:57,277 - INFO - SPY.py:2473 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-04 22:37:57,277 - INFO - SPY.py:2482 - IRX (3-month Treasury yield) converted from percentage to decimal format
2025-09-04 22:37:57,278 - INFO - SPY.py:2485 - IRX (3-month Treasury yield) after conversion to decimal: min=0.040180, max=0.042580, median=0.042180
2025-09-04 22:37:57,279 - INFO - SPY.py:2614 - Processed IRX (3-month Treasury yield) close values - first: 0.0423199987411499, last: 0.0401800012588501
2025-09-04 22:37:57,279 - INFO - SPY.py:2621 - VALIDATION: Final IRX (3-month Treasury yield) rate (decimal format): 0.040180 (4.0180%)
2025-09-04 22:37:57,279 - INFO - SPY.py:2647 - Market index ^IRX: Keeping only ['close_IRX']
2025-09-04 22:37:57,281 - INFO - SPY.py:2688 - Processed ^IRX data shape: (61, 1). Columns: ['close_IRX']
2025-09-04 22:37:57,281 - INFO - SPY.py:2689 - Data quality check: 0/61 (0.0%) zero values
2025-09-04 22:37:57,281 - INFO - SPY.py:1161 - VALIDATION: ^IRX last value: 0.0401800012588501
2025-09-04 22:37:57,281 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^IRX_1756996676 SUCCESS in 1.17s [Ticker: ^IRX] [Operation: refactored_fetch]
2025-09-04 22:37:57,281 - INFO - test_dual_vix_integration.py:69 - Successfully fetched 61 rows for ^IRX
2025-09-04 22:37:57,281 - INFO - test_dual_vix_integration.py:56 - Fetching data for ^TNX...
2025-09-04 22:37:58,082 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-04 22:37:58,556 - INFO - SPY.py:2508 - Using min_periods_override=30 for ^TNX
2025-09-04 22:37:58,557 - INFO - SPY.py:2472 - Applying standardized Treasury rate validation for TNX (10-year Treasury yield)
2025-09-04 22:37:58,558 - INFO - SPY.py:2473 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-04 22:37:58,558 - INFO - SPY.py:2482 - TNX (10-year Treasury yield) converted from percentage to decimal format
2025-09-04 22:37:58,558 - INFO - SPY.py:2485 - TNX (10-year Treasury yield) after conversion to decimal: min=0.041960, max=0.045100, median=0.043300
2025-09-04 22:37:58,559 - INFO - SPY.py:2614 - Processed TNX (10-year Treasury yield) close values - first: 0.04510000228881836, last: 0.04210999965667725
2025-09-04 22:37:58,559 - INFO - SPY.py:2621 - VALIDATION: Final TNX (10-year Treasury yield) rate (decimal format): 0.042110 (4.2110%)
2025-09-04 22:37:58,560 - INFO - SPY.py:2647 - Market index ^TNX: Keeping only ['close_TNX']
2025-09-04 22:37:58,561 - INFO - SPY.py:2688 - Processed ^TNX data shape: (61, 1). Columns: ['close_TNX']
2025-09-04 22:37:58,561 - INFO - SPY.py:2689 - Data quality check: 0/61 (0.0%) zero values
2025-09-04 22:37:58,561 - INFO - SPY.py:1161 - VALIDATION: ^TNX last value: 0.04210999965667725
2025-09-04 22:37:58,561 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^TNX_1756996677 SUCCESS in 1.28s [Ticker: ^TNX] [Operation: refactored_fetch]
2025-09-04 22:37:58,562 - INFO - test_dual_vix_integration.py:69 - Successfully fetched 61 rows for ^TNX
2025-09-04 22:37:58,562 - INFO - test_dual_vix_integration.py:72 - Step 2: Creating combined features...
2025-09-04 22:37:58,565 - INFO - SPY.py:2863 - Created master index with 61 unique dates from 2025-06-06 to 2025-09-03
2025-09-04 22:37:58,565 - INFO - SPY.py:2868 - Processing ticker SPY for reindexing: shape=(61, 4), columns=['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-04 22:37:58,565 - INFO - SPY.py:2874 - About to reindex SPY with master_index length 61
2025-09-04 22:37:58,566 - INFO - SPY.py:2749 - Index overlap for SPY: 100.0% (61/61 dates)
2025-09-04 22:37:58,568 - INFO - SPY.py:2812 - Successfully reindexed SPY with validation passed
2025-09-04 22:37:58,568 - INFO - SPY.py:2878 - Successfully reindexed SPY to master index. New shape: (61, 4)
2025-09-04 22:37:58,568 - INFO - SPY.py:2868 - Processing ticker ^VIX for reindexing: shape=(61, 1), columns=['close_VIX']
2025-09-04 22:37:58,569 - INFO - SPY.py:2874 - About to reindex ^VIX with master_index length 61
2025-09-04 22:37:58,569 - INFO - SPY.py:2749 - Index overlap for ^VIX: 100.0% (61/61 dates)
2025-09-04 22:37:58,570 - INFO - SPY.py:2812 - Successfully reindexed ^VIX with validation passed
2025-09-04 22:37:58,570 - INFO - SPY.py:2878 - Successfully reindexed ^VIX to master index. New shape: (61, 1)
2025-09-04 22:37:58,570 - INFO - SPY.py:2868 - Processing ticker ^VIX3M for reindexing: shape=(61, 1), columns=['close_VIX3M']
2025-09-04 22:37:58,570 - INFO - SPY.py:2874 - About to reindex ^VIX3M with master_index length 61
2025-09-04 22:37:58,570 - INFO - SPY.py:2749 - Index overlap for ^VIX3M: 100.0% (61/61 dates)
2025-09-04 22:37:58,571 - INFO - SPY.py:2812 - Successfully reindexed ^VIX3M with validation passed
2025-09-04 22:37:58,571 - INFO - SPY.py:2878 - Successfully reindexed ^VIX3M to master index. New shape: (61, 1)
2025-09-04 22:37:58,572 - INFO - SPY.py:2868 - Processing ticker ^IRX for reindexing: shape=(61, 1), columns=['close_IRX']
2025-09-04 22:37:58,572 - INFO - SPY.py:2874 - About to reindex ^IRX with master_index length 61
2025-09-04 22:37:58,572 - INFO - SPY.py:2749 - Index overlap for ^IRX: 100.0% (61/61 dates)
2025-09-04 22:37:58,573 - INFO - SPY.py:2812 - Successfully reindexed ^IRX with validation passed
2025-09-04 22:37:58,573 - INFO - SPY.py:2878 - Successfully reindexed ^IRX to master index. New shape: (61, 1)
2025-09-04 22:37:58,573 - INFO - SPY.py:2868 - Processing ticker ^TNX for reindexing: shape=(61, 1), columns=['close_TNX']
2025-09-04 22:37:58,574 - INFO - SPY.py:2874 - About to reindex ^TNX with master_index length 61
2025-09-04 22:37:58,574 - INFO - SPY.py:2749 - Index overlap for ^TNX: 100.0% (61/61 dates)
2025-09-04 22:37:58,575 - INFO - SPY.py:2812 - Successfully reindexed ^TNX with validation passed
2025-09-04 22:37:58,575 - INFO - SPY.py:2878 - Successfully reindexed ^TNX to master index. New shape: (61, 1)
2025-09-04 22:37:58,575 - INFO - SPY.py:2896 - Starting combine features with SPY shape: (61, 4)
2025-09-04 22:37:58,575 - INFO - SPY.py:2897 - Available tickers in reindexed_data_dict: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-04 22:37:58,575 - INFO - SPY.py:2913 - Merging ^VIX (Shape: (61, 1), Columns: ['close_VIX'])
2025-09-04 22:37:58,575 - INFO - SPY.py:2923 - Columns for ^VIX already appear to be renamed. Using as-is.
2025-09-04 22:37:58,576 - INFO - SPY.py:2959 - SPY index range: 2025-06-06 to 2025-09-03, count: 61
2025-09-04 22:37:58,576 - INFO - SPY.py:2960 - ^VIX index range: 2025-06-06 to 2025-09-03, count: 61
2025-09-04 22:37:58,577 - INFO - SPY.py:2977 - ^VIX close_VIX sample after join (first 3): [16.770000457763672, 17.15999984741211, 16.950000762939453], last: 16.350000381469727
2025-09-04 22:37:58,577 - INFO - SPY.py:2995 - After joining ^VIX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX']
2025-09-04 22:37:58,577 - INFO - SPY.py:2913 - Merging ^VIX3M (Shape: (61, 1), Columns: ['close_VIX3M'])
2025-09-04 22:37:58,577 - INFO - SPY.py:2923 - Columns for ^VIX3M already appear to be renamed. Using as-is.
2025-09-04 22:37:58,578 - INFO - SPY.py:2959 - SPY index range: 2025-06-06 to 2025-09-03, count: 61
2025-09-04 22:37:58,578 - INFO - SPY.py:2960 - ^VIX3M index range: 2025-06-06 to 2025-09-03, count: 61
2025-09-04 22:37:58,579 - INFO - SPY.py:2977 - ^VIX3M close_VIX3M sample after join (first 3): [20.020000457763672, 20.09000015258789, 19.940000534057617], last: 18.93000030517578
2025-09-04 22:37:58,580 - INFO - SPY.py:2995 - After joining ^VIX3M, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M']
2025-09-04 22:37:58,580 - INFO - SPY.py:2913 - Merging ^IRX (Shape: (61, 1), Columns: ['close_IRX'])
2025-09-04 22:37:58,580 - INFO - SPY.py:2923 - Columns for ^IRX already appear to be renamed. Using as-is.
2025-09-04 22:37:58,581 - INFO - SPY.py:2959 - SPY index range: 2025-06-06 to 2025-09-03, count: 61
2025-09-04 22:37:58,581 - INFO - SPY.py:2960 - ^IRX index range: 2025-06-06 to 2025-09-03, count: 61
2025-09-04 22:37:58,582 - INFO - SPY.py:2977 - ^IRX close_IRX sample after join (first 3): [0.0423199987411499, 0.04239999771118164, 0.0425], last: 0.0401800012588501
2025-09-04 22:37:58,582 - INFO - SPY.py:2995 - After joining ^IRX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX']
2025-09-04 22:37:58,582 - INFO - SPY.py:2913 - Merging ^TNX (Shape: (61, 1), Columns: ['close_TNX'])
2025-09-04 22:37:58,582 - INFO - SPY.py:2923 - Columns for ^TNX already appear to be renamed. Using as-is.
2025-09-04 22:37:58,583 - INFO - SPY.py:2959 - SPY index range: 2025-06-06 to 2025-09-03, count: 61
2025-09-04 22:37:58,583 - INFO - SPY.py:2960 - ^TNX index range: 2025-06-06 to 2025-09-03, count: 61
2025-09-04 22:37:58,584 - INFO - SPY.py:2977 - ^TNX close_TNX sample after join (first 3): [0.04510000228881836, 0.044819998741149905, 0.044739999771118165], last: 0.04210999965667725
2025-09-04 22:37:58,584 - INFO - SPY.py:2995 - After joining ^TNX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX']
2025-09-04 22:37:58,584 - INFO - SPY.py:3006 - Shape after merging all tickers: (61, 8)
2025-09-04 22:37:58,585 - INFO - SPY.py:3189 - Skipping VIX derived feature calculations - using reduced feature set
2025-09-04 22:37:58,586 - INFO - SPY.py:3193 - Skipping Treasury derived feature calculations - using reduced feature set
2025-09-04 22:37:58,586 - INFO - SPY.py:3092 - Volume column volume_SPY not found, skipping volume normalization
2025-09-04 22:37:58,586 - INFO - SPY.py:3095 - Performing final validation and cleanup...
2025-09-04 22:37:58,587 - INFO - SPY.py:3110 - Created expanded feature set with shape: (61, 6)
2025-09-04 22:37:58,587 - INFO - SPY.py:3111 - Expanded feature columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M']
2025-09-04 22:37:58,587 - INFO - SPY.py:3116 - Stored full market data for option pricing with shape: (61, 8)
2025-09-04 22:37:58,587 - INFO - SPY.py:3117 - Full market data columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX']
2025-09-04 22:37:58,588 - INFO - SPY.py:3121 - Expected columns for expanded feature set: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M']
2025-09-04 22:37:58,588 - INFO - SPY.py:3148 - VIX data validation passed: min=14.22, max=21.60, mean=16.65
2025-09-04 22:37:58,588 - INFO - SPY.py:3164 - VIX3M data validation passed: min=17.72, max=22.91, mean=19.49
2025-09-04 22:37:58,588 - INFO - SPY.py:3211 - Skipping feature distribution validation - using expanded feature set with SPY OHLC + VIX + VIX3M
2025-09-04 22:37:58,589 - INFO - SPY.py:3173 - Final expanded features shape: (61, 6)
2025-09-04 22:37:58,589 - INFO - SPY.py:3174 - Final expanded columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M']
2025-09-04 22:37:58,589 - INFO - test_dual_vix_integration.py:80 - Combined features shape: (61, 6)
2025-09-04 22:37:58,589 - INFO - test_dual_vix_integration.py:81 - Combined features columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M']
2025-09-04 22:37:58,589 - INFO - test_dual_vix_integration.py:84 - Step 3: Verifying dual VIX inclusion...
2025-09-04 22:37:58,590 - INFO - test_dual_vix_integration.py:102 - Step 4: Verifying VIX data quality...
2025-09-04 22:37:58,590 - INFO - test_dual_vix_integration.py:112 - VIX data statistics: {'count': 61, 'min': 14.220000267028809, 'max': 21.600000381469727, 'mean': 16.651147498459114, 'null_count': 0}
2025-09-04 22:37:58,590 - INFO - test_dual_vix_integration.py:119 - Step 5: Verifying VIX3M data quality...
2025-09-04 22:37:58,590 - INFO - test_dual_vix_integration.py:129 - VIX3M data statistics: {'count': 61, 'min': 17.719999313354492, 'max': 22.90999984741211, 'mean': 19.487377072944017, 'null_count': 0}
2025-09-04 22:37:58,591 - INFO - test_dual_vix_integration.py:136 - Step 6: Verifying feature count...
2025-09-04 22:37:58,591 - INFO - test_dual_vix_integration.py:145 - Step 7: Comparing VIX vs VIX3M characteristics...
2025-09-04 22:37:58,593 - INFO - test_dual_vix_integration.py:154 - VIX vs VIX3M comparison: {'VIX_mean': 16.651147498459114, 'VIX3M_mean': 19.487377072944017, 'VIX_std': 1.6591706253027814, 'VIX3M_std': 1.1688952851883911, 'correlation': 0.9786194457959431}
2025-09-04 22:37:58,593 - INFO - test_dual_vix_integration.py:156 - === Dual VIX Integration Test PASSED ===
2025-09-04 22:37:58,594 - INFO - test_dual_vix_integration.py:157 - Successfully integrated both VIX and VIX3M into feature set
2025-09-04 22:37:58,594 - INFO - test_dual_vix_integration.py:158 - Feature set contains 6 features: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M']
2025-09-04 22:37:58,594 - INFO - test_dual_vix_integration.py:159 - VIX (30-day): mean=16.65, range=14.22-21.60
2025-09-04 22:37:58,594 - INFO - test_dual_vix_integration.py:160 - VIX3M (3-month): mean=19.49, range=17.72-22.91
