#!/usr/bin/env python3
"""
Quick check to verify VIX integration is working correctly.
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_vix_feature():
    """Check if VIX is correctly integrated as a market feature."""
    try:
        from SPY import (
            fetch_historical_data_yf_refactored,
            create_combined_features,
            config
        )
        
        print("=== VIX Feature Integration Check ===")
        
        # Test with recent data (last 30 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        print(f"Testing with date range: {start_str} to {end_str}")
        
        # Fetch data for key tickers
        tickers = ['SPY', '^VIX']  # Just test the essential ones
        data_dict = {}
        
        for ticker in tickers:
            print(f"Fetching {ticker}...")
            df = fetch_historical_data_yf_refactored(
                ticker=ticker,
                start=start_str,
                end=end_str,
                min_periods_override=20
            )
            
            if df.empty:
                print(f"❌ Failed to fetch {ticker}")
                return False
            else:
                data_dict[ticker] = df
                print(f"✅ {ticker}: {len(df)} rows")
        
        # Add other required tickers with minimal data for testing
        for ticker in ['^VIX3M', '^IRX', '^TNX']:
            print(f"Fetching {ticker}...")
            df = fetch_historical_data_yf_refactored(
                ticker=ticker,
                start=start_str,
                end=end_str,
                min_periods_override=20
            )
            if not df.empty:
                data_dict[ticker] = df
                print(f"✅ {ticker}: {len(df)} rows")
            else:
                print(f"⚠️  {ticker}: No data (optional)")
        
        # Test feature creation
        print("\nCreating combined features...")
        features_df = create_combined_features(data_dict)
        
        if features_df.empty:
            print("❌ Features DataFrame is empty")
            return False
        
        print(f"✅ Features created successfully!")
        print(f"   Shape: {features_df.shape}")
        print(f"   Columns: {features_df.columns.tolist()}")
        
        # Check VIX specifically
        if 'close_VIX' in features_df.columns:
            vix_data = features_df['close_VIX']
            print(f"✅ VIX feature found!")
            print(f"   VIX range: {vix_data.min():.2f} - {vix_data.max():.2f}")
            print(f"   VIX mean: {vix_data.mean():.2f}")
            print(f"   Latest VIX: {vix_data.iloc[-1]:.2f}")
            
            # Check if VIX values are reasonable
            if 5 <= vix_data.min() <= 150 and 5 <= vix_data.max() <= 150:
                print("✅ VIX values are in reasonable range")
            else:
                print("⚠️  VIX values outside typical range")
        else:
            print("❌ VIX feature missing from final feature set")
            return False
        
        # Verify feature count
        expected_features = ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX']
        if list(features_df.columns) == expected_features:
            print("✅ Feature set matches expected: SPY OHLC + VIX")
        else:
            print(f"⚠️  Feature set mismatch. Expected: {expected_features}")
            print(f"   Got: {list(features_df.columns)}")
        
        print("\n=== VIX Integration Status: SUCCESS ===")
        return True
        
    except Exception as e:
        print(f"❌ Error during check: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_vix_feature()
    if success:
        print("\n🎉 VIX daily close price is correctly integrated as a market feature!")
    else:
        print("\n💥 VIX integration has issues that need to be addressed.")
